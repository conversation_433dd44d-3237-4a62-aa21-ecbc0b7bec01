# 通用软件授权管理平台 (Universal License Management Platform)

## 项目概括
本项目旨在开发一个基于C#/.NET的通用软件授权管理平台，为各种终端应用（桌面应用、移动APP等）提供完整的授权许可证管理服务。平台支持在线/离线激活、细粒度功能权限控制，并提供用户友好的Web管理界面和标准化API接口。

## 技术选型
- **主要编程语言**: C# (.NET 8.0)
- **Web框架**: ASP.NET Core 8.0
- **前端技术**: Blazor Server + Bootstrap 5
- **数据库**: SQL Server (主库) + Redis (缓存)
- **ORM框架**: Entity Framework Core 8.0
- **身份认证**: ASP.NET Core Identity + JWT
- **API文档**: Swagger/OpenAPI
- **加密算法**: RSA + AES (License加密)
- **日志框架**: Serilog
- **容器化**: Docker + Docker Compose
- **版本控制**: Git
- **测试框架**: xUnit + Moq
- **CI/CD**: GitHub Actions (可选)

## 项目结构 / 模块划分
```
LicenseManagementPlatform/
├── src/
│   ├── LMP.Core/                    # 核心业务逻辑层
│   │   ├── Entities/               # 实体模型
│   │   ├── Interfaces/             # 接口定义
│   │   ├── Services/               # 业务服务
│   │   └── DTOs/                   # 数据传输对象
│   ├── LMP.Infrastructure/          # 基础设施层
│   │   ├── Data/                   # 数据访问
│   │   ├── Security/               # 加密解密服务
│   │   ├── Cache/                  # 缓存服务
│   │   └── External/               # 外部服务集成
│   ├── LMP.WebAPI/                 # Web API层
│   │   ├── Controllers/            # API控制器
│   │   ├── Middleware/             # 中间件
│   │   └── Configuration/          # 配置
│   ├── LMP.WebUI/                  # Blazor Web界面
│   │   ├── Pages/                  # 页面组件
│   │   ├── Components/             # 共享组件
│   │   └── Services/               # 前端服务
│   └── LMP.ClientSDK/              # 客户端SDK
│       ├── Online/                 # 在线验证SDK
│       ├── Offline/                # 离线验证SDK
│       └── Models/                 # 客户端模型
├── tests/
│   ├── LMP.Core.Tests/             # 核心逻辑测试
│   ├── LMP.Infrastructure.Tests/   # 基础设施测试
│   └── LMP.WebAPI.Tests/           # API测试
├── docs/                           # 项目文档
├── scripts/                        # 部署脚本
├── docker-compose.yml              # Docker编排文件
└── LicenseManagementPlatform.sln   # 解决方案文件
```

## 核心功能 / 模块详解
### 1. License管理模块 (LicenseManagement)
- **生成License**: 为不同应用生成唯一的授权许可证，支持RSA数字签名
- **批量管理**: 支持批量生成、导入、导出License
- **状态管理**: 管理License的激活、过期、吊销状态
- **有效期控制**: 支持永久、定期、试用等多种有效期类型

### 2. 激活验证模块 (ActivationModule)
- **在线激活**: 实时API验证，支持设备绑定和并发控制
- **离线激活**: 生成离线验证文件，支持无网络环境使用
- **混合模式**: 智能切换在线/离线验证方式
- **设备管理**: 管理已激活设备信息和设备指纹

### 3. 功能权限模块 (FeaturePermission)
- **功能导入**: 支持从配置文件或API导入应用功能列表
- **权限配置**: 为不同License配置功能权限组合
- **动态控制**: 支持运行时动态调整功能权限
- **权限验证**: 提供细粒度的功能权限验证API

### 4. 应用管理模块 (ApplicationManagement)
- **应用注册**: 注册和管理不同的终端应用
- **版本控制**: 管理应用的不同版本和对应的功能列表
- **API密钥**: 为每个应用生成唯一的API访问密钥
- **统计报告**: 提供应用使用情况的详细统计

### 5. 用户管理模块 (UserManagement)
- **多租户支持**: 支持多个组织/客户独立管理
- **角色权限**: 管理员、操作员等不同角色权限控制
- **审计日志**: 记录所有关键操作的审计日志

## 数据模型设计
### 核心实体关系
```
Application (应用) 1:N License (许可证)
License 1:N LicenseFeature (许可证功能)
Application 1:N Feature (功能)
License 1:N ActivationRecord (激活记录)
User 1:N Application (用户应用)
```

### 主要数据表结构
- **Applications**: { Id, Name, Version, ApiKey, UserId, CreatedAt, UpdatedAt }
- **Licenses**: { Id, ApplicationId, LicenseKey, LicenseType, ExpiryDate, MaxActivations, Status, CreatedAt }
- **Features**: { Id, ApplicationId, FeatureName, FeatureCode, Description, IsEnabled }
- **LicenseFeatures**: { LicenseId, FeatureId, IsEnabled }
- **ActivationRecords**: { Id, LicenseId, DeviceId, DeviceFingerprint, ActivatedAt, LastVerifiedAt, Status }
- **Users**: { Id, Username, Email, PasswordHash, Role, OrganizationId, CreatedAt }

## API设计概览
### 管理端API (Management API)
- `POST /api/licenses` - 创建License
- `GET /api/licenses/{id}` - 获取License详情
- `PUT /api/licenses/{id}/status` - 更新License状态
- `POST /api/applications` - 注册应用
- `GET /api/applications/{id}/features` - 获取应用功能列表

### 客户端验证API (Verification API)
- `POST /api/verify/online` - 在线License验证
- `POST /api/verify/activate` - 激活License
- `POST /api/verify/features` - 验证功能权限
- `GET /api/verify/offline-data/{licenseKey}` - 获取离线验证数据

## 技术实现细节
[本部分将在后续开发每一个模块/功能时，自动填充具体的技术实现方案、关键代码片段说明、加密算法实现、API端点详细设计等内容。]

## 开发状态跟踪
| 模块/功能                    | 状态   | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------------|--------|--------|--------------|--------------|------------|
| 项目基础架构搭建             | 未开始 | AI     | 2024-01-15   |              |            |
| 数据库设计与Entity Framework配置 | 未开始 | AI     | 2024-01-18   |              |            |
| 用户管理模块                 | 未开始 | AI     | 2024-01-20   |              |            |
| 应用管理模块                 | 未开始 | AI     | 2024-01-22   |              |            |
| License管理模块              | 未开始 | AI     | 2024-01-25   |              |            |
| 加密解密服务                 | 未开始 | AI     | 2024-01-27   |              |            |
| 在线激活验证模块             | 未开始 | AI     | 2024-01-30   |              |            |
| 离线激活验证模块             | 未开始 | AI     | 2024-02-02   |              |            |
| 功能权限管理模块             | 未开始 | AI     | 2024-02-05   |              |            |
| Web管理界面(Blazor)          | 未开始 | AI     | 2024-02-10   |              |            |
| 客户端SDK开发                | 未开始 | AI     | 2024-02-12   |              |            |
| API文档与测试                | 未开始 | AI     | 2024-02-15   |              |            |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

## 环境设置与运行指南
### 开发环境要求
- .NET 8.0 SDK
- SQL Server 2019+ 或 SQL Server Express
- Redis Server
- Visual Studio 2022 或 VS Code
- Docker Desktop (可选)

### 运行步骤
[具体的安装和运行步骤将在项目开发过程中逐步完善]

## 部署指南
[包含Docker部署、IIS部署等多种部署方式的详细说明]

## 安全考虑
- License密钥采用RSA-2048位加密
- API访问采用JWT Token认证
- 敏感数据传输使用HTTPS
- 数据库连接字符串加密存储
- 定期安全审计和漏洞扫描
