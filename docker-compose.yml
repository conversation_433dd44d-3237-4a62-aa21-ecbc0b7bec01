version: '3.8'

services:
  # SQL Server 数据库
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: lmp-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=LMP@2024!Strong
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - lmp-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: lmp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lmp-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Web API 服务
  webapi:
    build:
      context: .
      dockerfile: src/LMP.WebAPI/Dockerfile
    container_name: lmp-webapi
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=LicenseManagementDB;User Id=sa;Password=LMP@2024!Strong;TrustServerCertificate=true;
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5000:80"
    depends_on:
      - sqlserver
      - redis
    networks:
      - lmp-network
    restart: unless-stopped

  # Blazor Web UI
  webui:
    build:
      context: .
      dockerfile: src/LMP.WebUI/Dockerfile
    container_name: lmp-webui
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ApiSettings__BaseUrl=http://webapi:80
    ports:
      - "5001:80"
    depends_on:
      - webapi
    networks:
      - lmp-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local

networks:
  lmp-network:
    driver: bridge
