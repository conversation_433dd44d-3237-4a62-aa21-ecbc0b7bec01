{"version": 2, "dgSpecHash": "FjRWv75T8Uk=", "success": false, "projectFilePath": "D:\\00 Crack\\tests\\LMP.WebAPI.Tests\\LMP.WebAPI.Tests.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1008", "level": "Error", "message": "使用中央包版本管理的项目不应定义 PackageReference 项上的版本，但应定义 PackageVersion 项上的版本: xunit.runner.visualstudio;Microsoft.NET.Test.Sdk;xunit;coverlet.collector。", "projectPath": "D:\\00 Crack\\tests\\LMP.WebAPI.Tests\\LMP.WebAPI.Tests.csproj", "filePath": "D:\\00 Crack\\tests\\LMP.WebAPI.Tests\\LMP.WebAPI.Tests.csproj", "targetGraphs": []}]}