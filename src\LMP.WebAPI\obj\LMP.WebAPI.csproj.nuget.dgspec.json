{"format": 1, "restore": {"D:\\00 Crack\\src\\LMP.WebAPI\\LMP.WebAPI.csproj": {}}, "projects": {"D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj", "projectName": "LMP.Core", "projectPath": "D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\src\\LMP.Core\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "centralPackageVersions": {"AspNetCore.HealthChecks.Redis": "7.0.1", "AspNetCore.HealthChecks.SqlServer": "7.0.0", "AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BCrypt.Net-Next": "4.0.3", "coverlet.collector": "6.0.0", "FluentValidation": "11.8.1", "FluentValidation.AspNetCore": "11.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0", "Microsoft.AspNetCore.Mvc.Testing": "8.0.0", "Microsoft.AspNetCore.OpenApi": "8.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.InMemory": "8.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "Newtonsoft.Json": "13.0.3", "Serilog": "3.1.1", "Serilog.AspNetCore": "8.0.0", "Serilog.Sinks.Console": "5.0.1", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.MSSqlServer": "6.5.0", "StackExchange.Redis": "2.7.10", "Swashbuckle.AspNetCore": "6.5.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Text.Json": "8.0.0", "xunit": "2.6.2", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\00 Crack\\src\\LMP.Infrastructure\\LMP.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\src\\LMP.Infrastructure\\LMP.Infrastructure.csproj", "projectName": "LMP.Infrastructure", "projectPath": "D:\\00 Crack\\src\\LMP.Infrastructure\\LMP.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\src\\LMP.Infrastructure\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj": {"projectPath": "D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "centralPackageVersions": {"AspNetCore.HealthChecks.Redis": "7.0.1", "AspNetCore.HealthChecks.SqlServer": "7.0.0", "AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BCrypt.Net-Next": "4.0.3", "coverlet.collector": "6.0.0", "FluentValidation": "11.8.1", "FluentValidation.AspNetCore": "11.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0", "Microsoft.AspNetCore.Mvc.Testing": "8.0.0", "Microsoft.AspNetCore.OpenApi": "8.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.InMemory": "8.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "Newtonsoft.Json": "13.0.3", "Serilog": "3.1.1", "Serilog.AspNetCore": "8.0.0", "Serilog.Sinks.Console": "5.0.1", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.MSSqlServer": "6.5.0", "StackExchange.Redis": "2.7.10", "Swashbuckle.AspNetCore": "6.5.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Text.Json": "8.0.0", "xunit": "2.6.2", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\00 Crack\\src\\LMP.WebAPI\\LMP.WebAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\src\\LMP.WebAPI\\LMP.WebAPI.csproj", "projectName": "LMP.WebAPI", "projectPath": "D:\\00 Crack\\src\\LMP.WebAPI\\LMP.WebAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\src\\LMP.WebAPI\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj": {"projectPath": "D:\\00 Crack\\src\\LMP.Core\\LMP.Core.csproj"}, "D:\\00 Crack\\src\\LMP.Infrastructure\\LMP.Infrastructure.csproj": {"projectPath": "D:\\00 Crack\\src\\LMP.Infrastructure\\LMP.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.5, )"}}, "centralPackageVersions": {"AspNetCore.HealthChecks.Redis": "7.0.1", "AspNetCore.HealthChecks.SqlServer": "7.0.0", "AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BCrypt.Net-Next": "4.0.3", "coverlet.collector": "6.0.0", "FluentValidation": "11.8.1", "FluentValidation.AspNetCore": "11.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0", "Microsoft.AspNetCore.Mvc.Testing": "8.0.0", "Microsoft.AspNetCore.OpenApi": "8.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.InMemory": "8.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "Newtonsoft.Json": "13.0.3", "Serilog": "3.1.1", "Serilog.AspNetCore": "8.0.0", "Serilog.Sinks.Console": "5.0.1", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.MSSqlServer": "6.5.0", "StackExchange.Redis": "2.7.10", "Swashbuckle.AspNetCore": "6.5.0", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Text.Json": "8.0.0", "xunit": "2.6.2", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}